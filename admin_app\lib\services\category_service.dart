import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';
import '../models/category_model.dart';

class CategoryService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'categories';
  static const Uuid _uuid = Uuid();

  /// Get all categories with pagination
  static Future<List<CategoryModel>> getAllCategoriesPaginated({
    DocumentSnapshot? lastDocument,
    int limit = 20,
    bool? isActiveFilter,
    String? parentCategoryId,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply active filter
      if (isActiveFilter != null) {
        query = query.where('isActive', isEqualTo: isActiveFilter);
      }

      // Apply parent category filter
      if (parentCategoryId != null) {
        if (parentCategoryId == 'root') {
          query = query.where('parentCategoryId', isNull: true);
        } else {
          query = query.where('parentCategoryId', isEqualTo: parentCategoryId);
        }
      }

      // Order by sort order and creation date
      query = query.orderBy('sortOrder').orderBy('createdAt', descending: true);

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();
      List<CategoryModel> categories = querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();

      // Apply search filter if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        categories = categories.where((category) {
          return category.name.toLowerCase().contains(searchLower) ||
                 category.description.toLowerCase().contains(searchLower);
        }).toList();
      }

      return categories;
    } catch (e) {
      print('Error fetching categories: $e');
      return [];
    }
  }

  /// Get category by ID
  static Future<CategoryModel?> getCategoryById(String categoryId) async {
    try {
      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(categoryId)
          .get();

      if (docSnapshot.exists) {
        return CategoryModel.fromDocument(docSnapshot);
      }
      return null;
    } catch (e) {
      print('Error fetching category by ID: $e');
      return null;
    }
  }

  /// Add new category
  static Future<bool> addCategory(CategoryModel category) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(category.id)
          .set(category.toMap());

      // Update parent category's subcategory list if this is a subcategory
      if (category.parentCategoryId != null) {
        await _addSubcategoryToParent(category.parentCategoryId!, category.id);
      }

      return true;
    } catch (e) {
      print('Error adding category: $e');
      return false;
    }
  }

  /// Update category
  static Future<bool> updateCategory(CategoryModel category) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(category.id)
          .update(category.toMap());

      return true;
    } catch (e) {
      print('Error updating category: $e');
      return false;
    }
  }

  /// Delete category (soft delete)
  static Future<bool> deleteCategory(String categoryId) async {
    try {
      final category = await getCategoryById(categoryId);
      if (category == null) return false;

      // Check if category has subcategories
      if (category.hasSubcategories) {
        throw Exception('Cannot delete category with subcategories. Please delete subcategories first.');
      }

      // Soft delete
      await _firestore
          .collection(_collection)
          .doc(categoryId)
          .update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Remove from parent's subcategory list if this is a subcategory
      if (category.parentCategoryId != null) {
        await _removeSubcategoryFromParent(category.parentCategoryId!, categoryId);
      }

      return true;
    } catch (e) {
      print('Error deleting category: $e');
      return false;
    }
  }

  /// Permanently delete category
  static Future<bool> permanentlyDeleteCategory(String categoryId) async {
    try {
      final category = await getCategoryById(categoryId);
      if (category == null) return false;

      // Check if category has subcategories
      if (category.hasSubcategories) {
        throw Exception('Cannot delete category with subcategories. Please delete subcategories first.');
      }

      await _firestore
          .collection(_collection)
          .doc(categoryId)
          .delete();

      // Remove from parent's subcategory list if this is a subcategory
      if (category.parentCategoryId != null) {
        await _removeSubcategoryFromParent(category.parentCategoryId!, categoryId);
      }

      return true;
    } catch (e) {
      print('Error permanently deleting category: $e');
      return false;
    }
  }

  /// Get categories statistics
  static Future<Map<String, int>> getCategoriesStatistics() async {
    try {
      final allCategoriesSnapshot = await _firestore
          .collection(_collection)
          .get();

      final activeCategoriesSnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final parentCategoriesSnapshot = await _firestore
          .collection(_collection)
          .where('parentCategoryId', isNull: true)
          .get();

      return {
        'total': allCategoriesSnapshot.docs.length,
        'active': activeCategoriesSnapshot.docs.length,
        'inactive': allCategoriesSnapshot.docs.length - activeCategoriesSnapshot.docs.length,
        'parent': parentCategoriesSnapshot.docs.length,
        'subcategories': allCategoriesSnapshot.docs.length - parentCategoriesSnapshot.docs.length,
      };
    } catch (e) {
      print('Error fetching categories statistics: $e');
      return {
        'total': 0,
        'active': 0,
        'inactive': 0,
        'parent': 0,
        'subcategories': 0,
      };
    }
  }

  /// Get parent categories only
  static Future<List<CategoryModel>> getParentCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('parentCategoryId', isNull: true)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching parent categories: $e');
      return [];
    }
  }

  /// Get subcategories by parent ID
  static Future<List<CategoryModel>> getSubcategories(String parentCategoryId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('parentCategoryId', isEqualTo: parentCategoryId)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching subcategories: $e');
      return [];
    }
  }

  /// Update category sort order
  static Future<bool> updateCategorySortOrder(String categoryId, int sortOrder) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(categoryId)
          .update({
        'sortOrder': sortOrder,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      print('Error updating category sort order: $e');
      return false;
    }
  }

  /// Generate new category ID
  static String generateCategoryId() {
    return _uuid.v4();
  }

  /// Helper method to add subcategory to parent
  static Future<void> _addSubcategoryToParent(String parentId, String subcategoryId) async {
    await _firestore
        .collection(_collection)
        .doc(parentId)
        .update({
      'subcategoryIds': FieldValue.arrayUnion([subcategoryId]),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  /// Helper method to remove subcategory from parent
  static Future<void> _removeSubcategoryFromParent(String parentId, String subcategoryId) async {
    await _firestore
        .collection(_collection)
        .doc(parentId)
        .update({
      'subcategoryIds': FieldValue.arrayRemove([subcategoryId]),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  /// Search categories
  static Future<List<CategoryModel>> searchCategories({
    required String query,
    int limit = 50,
  }) async {
    try {
      if (query.isEmpty) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .orderBy('createdAt', descending: true)
          .limit(limit * 2) // Get more to filter
          .get();

      final allCategories = querySnapshot.docs
          .map((doc) => CategoryModel.fromDocument(doc))
          .toList();

      // Filter categories based on search query
      final searchLower = query.toLowerCase();
      return allCategories.where((category) {
        return category.name.toLowerCase().contains(searchLower) ||
               category.description.toLowerCase().contains(searchLower);
      }).take(limit).toList();
    } catch (e) {
      print('Error searching categories: $e');
      return [];
    }
  }
}
