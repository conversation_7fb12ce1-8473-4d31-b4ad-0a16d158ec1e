import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_constants.dart';

/// Reusable form field widget for product forms
class ProductFormField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hint;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final int maxLines;
  final bool enabled;
  final Widget? suffixIcon;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(String)? onChanged;

  const ProductFormField({
    super.key,
    required this.controller,
    required this.label,
    required this.hint,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.maxLines = 1,
    this.enabled = true,
    this.suffixIcon,
    this.inputFormatters,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          maxLines: maxLines,
          enabled: enabled,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.primaryColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.errorColor),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
          ),
        ),
      ],
    );
  }
}

/// Dropdown field for categories
class ProductCategoryDropdown extends StatelessWidget {
  final String? selectedCategory;
  final List<String> categories;
  final void Function(String?) onChanged;
  final String? Function(String?)? validator;

  const ProductCategoryDropdown({
    super.key,
    required this.selectedCategory,
    required this.categories,
    required this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        DropdownButtonFormField<String>(
          value: selectedCategory,
          onChanged: onChanged,
          validator: validator,
          decoration: InputDecoration(
            hintText: 'Select category',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.borderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
              borderSide: const BorderSide(color: AppConstants.primaryColor),
            ),
            contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
          ),
          items: categories.map((category) {
            return DropdownMenuItem<String>(
              value: category,
              child: Text(category),
            );
          }).toList(),
        ),
      ],
    );
  }
}

/// Switch widget for boolean fields
class ProductSwitchField extends StatelessWidget {
  final String label;
  final bool value;
  final void Function(bool) onChanged;
  final String? subtitle;

  const ProductSwitchField({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      child: SwitchListTile(
        title: Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        subtitle: subtitle != null ? Text(subtitle!) : null,
        value: value,
        onChanged: onChanged,
        activeColor: AppConstants.primaryColor,
      ),
    );
  }
}

/// Tags input widget
class ProductTagsInput extends StatefulWidget {
  final List<String> tags;
  final void Function(List<String>) onTagsChanged;

  const ProductTagsInput({
    super.key,
    required this.tags,
    required this.onTagsChanged,
  });

  @override
  State<ProductTagsInput> createState() => _ProductTagsInputState();
}

class _ProductTagsInputState extends State<ProductTagsInput> {
  final TextEditingController _tagController = TextEditingController();

  @override
  void dispose() {
    _tagController.dispose();
    super.dispose();
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !widget.tags.contains(tag)) {
      final newTags = [...widget.tags, tag];
      widget.onTagsChanged(newTags);
      _tagController.clear();
    }
  }

  void _removeTag(String tag) {
    final newTags = widget.tags.where((t) => t != tag).toList();
    widget.onTagsChanged(newTags);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tags',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: AppConstants.fontSizeMedium,
          ),
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _tagController,
                decoration: InputDecoration(
                  hintText: 'Enter tag and press add',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                  ),
                  contentPadding: const EdgeInsets.all(AppConstants.paddingMedium),
                ),
                onSubmitted: (_) => _addTag(),
              ),
            ),
            const SizedBox(width: AppConstants.paddingSmall),
            ElevatedButton(
              onPressed: _addTag,
              child: const Text('Add'),
            ),
          ],
        ),
        if (widget.tags.isNotEmpty) ...[
          const SizedBox(height: AppConstants.paddingSmall),
          Wrap(
            spacing: AppConstants.paddingSmall,
            runSpacing: AppConstants.paddingSmall,
            children: widget.tags.map((tag) {
              return Chip(
                label: Text(tag),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => _removeTag(tag),
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}

/// Image picker widget for product images
class ProductImagePicker extends StatelessWidget {
  final List<XFile> selectedImages;
  final void Function() onPickImages;
  final void Function(int) onRemoveImage;
  final int maxImages;

  const ProductImagePicker({
    super.key,
    required this.selectedImages,
    required this.onPickImages,
    required this.onRemoveImage,
    this.maxImages = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Product Images',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
            Text(
              '${selectedImages.length}/$maxImages',
              style: const TextStyle(
                color: AppConstants.textSecondaryColor,
                fontSize: AppConstants.fontSizeSmall,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: selectedImages.length + (selectedImages.length < maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == selectedImages.length) {
                // Add image button
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: InkWell(
                    onTap: onPickImages,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppConstants.borderColor,
                          style: BorderStyle.solid,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_photo_alternate,
                            size: 32,
                            color: AppConstants.textSecondaryColor,
                          ),
                          SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            'Add Image',
                            style: TextStyle(
                              color: AppConstants.textSecondaryColor,
                              fontSize: AppConstants.fontSizeSmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Image preview
              return Container(
                width: 120,
                margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      child: Image.network(
                        selectedImages[index].path,
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 120,
                            height: 120,
                            color: AppConstants.backgroundColor,
                            child: const Icon(
                              Icons.error,
                              color: AppConstants.errorColor,
                            ),
                          );
                        },
                      ),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: InkWell(
                        onTap: () => onRemoveImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: AppConstants.errorColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Image picker widget for editing products (handles both existing and new images)
class EditProductImagePicker extends StatelessWidget {
  final List<String> existingImages;
  final List<XFile> newImages;
  final void Function() onPickImages;
  final void Function(int) onRemoveNewImage;
  final void Function(int) onRemoveExistingImage;
  final int maxImages;

  const EditProductImagePicker({
    super.key,
    required this.existingImages,
    required this.newImages,
    required this.onPickImages,
    required this.onRemoveNewImage,
    required this.onRemoveExistingImage,
    this.maxImages = 10,
  });

  @override
  Widget build(BuildContext context) {
    final totalImages = existingImages.length + newImages.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Product Images',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
            Text(
              '$totalImages/$maxImages',
              style: const TextStyle(
                color: AppConstants.textSecondaryColor,
                fontSize: AppConstants.fontSizeSmall,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        Container(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: totalImages + (totalImages < maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == totalImages) {
                // Add image button
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: InkWell(
                    onTap: onPickImages,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppConstants.borderColor,
                          style: BorderStyle.solid,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_photo_alternate,
                            size: 32,
                            color: AppConstants.textSecondaryColor,
                          ),
                          SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            'Add Image',
                            style: TextStyle(
                              color: AppConstants.textSecondaryColor,
                              fontSize: AppConstants.fontSizeSmall,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              // Determine if this is an existing or new image
              if (index < existingImages.length) {
                // Existing image
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        child: CachedNetworkImage(
                          imageUrl: existingImages[index],
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 120,
                            height: 120,
                            color: AppConstants.backgroundColor,
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 120,
                            height: 120,
                            color: AppConstants.backgroundColor,
                            child: const Icon(
                              Icons.error,
                              color: AppConstants.errorColor,
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: InkWell(
                          onTap: () => onRemoveExistingImage(index),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: AppConstants.errorColor,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                // New image
                final newImageIndex = index - existingImages.length;
                return Container(
                  width: 120,
                  margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                        child: Image.network(
                          newImages[newImageIndex].path,
                          width: 120,
                          height: 120,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 120,
                              color: AppConstants.backgroundColor,
                              child: const Icon(
                                Icons.error,
                                color: AppConstants.errorColor,
                              ),
                            );
                          },
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: InkWell(
                          onTap: () => onRemoveNewImage(newImageIndex),
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: AppConstants.errorColor,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      // Badge to indicate new image
                      Positioned(
                        top: 4,
                        left: 4,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppConstants.successColor,
                            borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                          ),
                          child: const Text(
                            'NEW',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }
}
